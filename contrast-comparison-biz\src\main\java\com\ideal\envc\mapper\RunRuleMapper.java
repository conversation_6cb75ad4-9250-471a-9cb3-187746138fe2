package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.entity.RunRuleEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 节点规则结果Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface RunRuleMapper {
    /**
     * 查询节点规则结果
     *
     * @param id 节点规则结果主键
     * @return 节点规则结果
     */
    RunRuleEntity selectRunRuleById(Long id);

    /**
     * 查询节点规则结果列表
     *
     * @param runRule 节点规则结果
     * @return 节点规则结果集合
     */
    List<RunRuleEntity> selectRunRuleList(RunRuleEntity runRule);

    /**
     * 新增节点规则结果
     *
     * @param runRule 节点规则结果
     * @return 结果
     */
    int insertRunRule(RunRuleEntity runRule);

    /**
     * 修改节点规则结果
     *
     * @param runRule 节点规则结果
     * @return 结果
     */
    int updateRunRule(RunRuleEntity runRule);

    /**
     * 修改节点规则结果
     * @param result
     * @param id
     * @return
     */
    int updateRunRuleOfResult(@Param("result") Integer result, @Param("id") Long id);

    /**
     * 删除节点规则结果
     *
     * @param id 节点规则结果主键
     * @return 结果
     */
    int deleteRunRuleById(Long id);

    /**
     * 批量删除节点规则结果
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRunRuleByIds(Long[] ids);

    /**
     * 根据运行实例信息ID查询关联的运行规则列表
     *
     * @param instanceInfoId 运行实例信息ID
     * @return 运行规则列表
     */
    List<RunRuleEntity> selectRulesByInstanceInfoId(@Param("instanceInfoId") Long instanceInfoId);
}
