package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IResultMonitorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 比对结果-比对详情
 * <AUTHOR>
 */

@RestController
@RequestMapping("/resultDetail")
public class ResultDetailController {
    private static final Logger logger = LoggerFactory.getLogger(ResultDetailController.class);
    private final IResultMonitorService resultMonitorService;

    public ResultDetailController(IResultMonitorService resultMonitorService) {
        this.resultMonitorService = resultMonitorService;
    }

    @PostMapping("/compareFileContent")
    public R<String> detail(@RequestParam Long flowId,@RequestParam Integer que) {
        logger.info("查询比对详情，flowId：{},que:{}", flowId,que);

        try {
            if(que==null){
                que=0;
            }
            String content = "";
            try{
                content = resultMonitorService.selectContentForCompareFileByFlowId(flowId);
            }catch (ContrastBusinessException e){
                content = e.getMessage();
            }
            String resultHtml ="";
            //全部
            if(que==0){
                resultHtml=content;
            }
            //差异展示
            else{
                String reg="<tr height=\"5\" class=\"cps_tr1\">";
                String regs ="<tr hidden height=\"5\" class=\"cps_tr1\">";

                String reg2 = "<tr class=\"cps_tr2\">";
                String reg2s="<tr hidden class=\"cps_tr2\">";
                resultHtml=content.replace(reg, regs);
                resultHtml = resultHtml.replace(reg2, reg2s);
            }

            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), resultHtml, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("detail 查询比对详情系统异常，flowId：{}，que:{}", flowId,que, e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }


    @PostMapping("/compareDirContent")
    public R<String> dirDetail(@RequestParam Long flowId) {
        logger.info("查询目录比对详情，flowId：{}", flowId);

        try {
            String content = resultMonitorService.selectContentForCompareFileByFlowId(flowId);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), content, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.warn("查询比对详情业务异常，flowId：{}，异常信息：{}", flowId, e.getMessage());
            return R.fail(ResponseCodeEnum.DATA_NOT_FOUND.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询比对详情系统异常，flowId：{}", flowId, e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
}
