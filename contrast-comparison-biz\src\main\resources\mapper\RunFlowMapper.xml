<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.RunFlowMapper">

    <resultMap type="com.ideal.envc.model.entity.RunFlowEntity" id="RunFlowResult">
            <result property="id" column="iid"/>
            <result property="flowid" column="iflowid"/>
            <result property="runBizId" column="irun_biz_id"/>
            <result property="model" column="imodel"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="createTime" column="icreate_time"/>
            <result property="endTime" column="iend_time"/>
            <result property="state" column="istate"/>
            <result property="elapsedTime" column="ielapsed_time"/>
            <result property="ret" column="iret"/>
            <result property="updateOrderTime" column="iupdate_order_time"/>
    </resultMap>

    <sql id="selectRunFlow">
        select iid, iflowid, irun_biz_id, imodel, icreator_id, icreator_name, icreate_time, iend_time, istate, ielapsed_time, iret, iupdate_order_time
        from ieai_envc_run_flow
    </sql>

    <select id="selectRunFlowList" parameterType="com.ideal.envc.model.entity.RunFlowEntity" resultMap="RunFlowResult">
        <include refid="selectRunFlow"/>
        <where>
                        <if test="flowid != null ">
                            and iflowid = #{flowid}
                        </if>
                        <if test="runBizId != null ">
                            and irun_biz_id = #{runBizId}
                        </if>
                        <if test="model != null ">
                            and imodel = #{model}
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
                        <if test="endTime != null ">
                            and iend_time = #{endTime}
                        </if>
                        <if test="state != null ">
                            and istate = #{state}
                        </if>
                        <if test="elapsedTime != null ">
                            and ielapsed_time = #{elapsedTime}
                        </if>
                        <if test="ret != null  and ret != ''">
                            and iret = #{ret}
                        </if>
                        <if test="updateOrderTime != null">
                            and iupdate_order_time = #{updateOrderTime}
                        </if>
        </where>
    </select>

    <select id="selectRunFlowById" parameterType="Long"
            resultMap="RunFlowResult">
            <include refid="selectRunFlow"/>
            where iid = #{id}
    </select>
    <select id="selectRunFlowByFlowId" parameterType="Long"
            resultMap="RunFlowResult">
            <include refid="selectRunFlow"/>
            where iflowid = #{flowId}
    </select>

    <insert id="insertRunFlow" parameterType="com.ideal.envc.model.entity.RunFlowEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_run_flow
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,</if>
                    <if test="flowid != null">iflowid,</if>
                    <if test="runBizId != null">irun_biz_id,</if>
                    <if test="model != null">imodel,</if>
                    <if test="creatorId != null">icreator_id,</if>
                    <if test="creatorName != null">icreator_name,</if>
                    icreate_time,
                    <if test="endTime != null">iend_time,</if>
                    <if test="state != null">istate,</if>
                    <if test="elapsedTime != null">ielapsed_time,</if>
                    <if test="ret != null">iret,</if>
                    <if test="updateOrderTime != null">iupdate_order_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="flowid != null">#{flowid},</if>
                    <if test="runBizId != null">#{runBizId},</if>
                    <if test="model != null">#{model},</if>
                    <if test="creatorId != null">#{creatorId},</if>
                    <if test="creatorName != null">#{creatorName},</if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="endTime != null">#{endTime},</if>
                    <if test="state != null">#{state},</if>
                    <if test="elapsedTime != null">#{elapsedTime},</if>
                    <if test="ret != null">#{ret},</if>
                    <if test="updateOrderTime != null">#{updateOrderTime},</if>
        </trim>
    </insert>

    <update id="updateRunFlow" parameterType="com.ideal.envc.model.entity.RunFlowEntity">
        update ieai_envc_run_flow
        <trim prefix="SET" suffixOverrides=",">
            <if test="flowid != null">iflowid = #{flowid},</if>
            <if test="runBizId != null">irun_biz_id = #{runBizId},</if>
            <if test="model != null">imodel = #{model},</if>
            <if test="endTime != null">iend_time = #{endTime},</if>
            <if test="state != null">istate = #{state},</if>
            <if test="elapsedTime != null">ielapsed_time = #{elapsedTime},</if>
            <if test="ret != null">iret = #{ret},</if>
            <if test="updateOrderTime != null">iupdate_order_time = #{updateOrderTime},</if>
        </trim>
        where iid = #{id}
    </update>

    <update id="updateRunFlowOfFirst" parameterType="com.ideal.envc.model.entity.RunFlowEntity">
        update ieai_envc_run_flow set iflowid = #{flowid} ,
                                      istate = 0,
                                      iupdate_order_time = #{updateOrderTime}
        where iid = #{id}
    </update>

    <delete id="deleteRunFlowById" parameterType="Long">
        delete
        from ieai_envc_run_flow where iid = #{id}
    </delete>

    <delete id="deleteRunFlowByIds" parameterType="String">
        delete from ieai_envc_run_flow where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>