package com.ideal.envc.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import java.util.List;

import com.ideal.envc.model.dto.TaskDto;
import com.ideal.envc.model.dto.TaskQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.TaskCronUpdateDto;
import com.ideal.envc.model.dto.TaskPlanListDto;
import com.ideal.envc.model.dto.TaskStartOrStopDto;
import com.ideal.envc.model.dto.TaskOperateResultDto;
import com.ideal.system.common.component.aop.MethodPermission;
import org.springframework.validation.annotation.Validated;
import com.ideal.envc.service.ITaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.enums.TaskOperateEnums;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 任务管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/task")
@MethodPermission("@dp.hasBtnPermission('schedule-task-management')")
public class TaskController {
    private final Logger logger = LoggerFactory.getLogger(TaskController.class);

    private final ITaskService taskService;
    private final UserinfoComponent userinfoComponent;

    public TaskController(ITaskService taskService, UserinfoComponent userinfoComponent) {
        this.taskService = taskService;
        this.userinfoComponent = userinfoComponent;
    }

    /**
     * 查询任务列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<TaskDto>> list(@RequestBody TableQueryDto<TaskQueryDto> tableQueryDto) {
        try {
            PageInfo<TaskDto> list = taskService.selectTaskList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("查询任务列表失败", e);
            return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询任务列表系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 查询任务详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<TaskDto> getTaskInfoInfo(@RequestParam(value = "id")Long id) {
        try {
            TaskDto taskDto = taskService.selectTaskDetailById(id);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), taskDto, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("查询任务详情失败", e);
            return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询任务详情系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 新增任务
     *
     * @param taskDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody TaskDto taskDto) {
        try {
            int rows = taskService.insertTask(taskDto);
            if (rows > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail("13001", "同一方案下已存在相同cron表达式的任务，请重新输入");
        } catch (RuntimeException e) {
            logger.error("新增任务失败", e);
            if (e.getMessage() != null && e.getMessage().contains("同一方案下已存在相同cron表达式的任务")) {
                return R.fail("13001", e.getMessage());
            }
            return R.fail("00000", e.getMessage());
        }
    }

    /**
     * 新增任务并立即启动
     *
     * @param taskDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/create")
    @MethodPermission("@dp.hasBtnPermission('saveScheduleCompare')")
    public R<Long> createTask(@RequestBody @Validated TaskDto taskDto) {
        logger.info("新增任务并立即启动，参数：{}", taskDto);

        try {
            UserDto userDto = userinfoComponent.getUser();
            Long taskId = taskService.createTask(taskDto, userDto);
            logger.info("新增任务并立即启动成功，任务ID：{}", taskId);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), taskId, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("新增任务并立即启动失败", e);
            if (e.getMessage().contains("同一方案下已存在相同cron表达式的任务")) {
                return R.fail(ResponseCodeEnum.DATA_ALREADY_EXISTS.getCode(), e.getMessage());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("新增任务并立即启动系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 修改保存任务
     *
     * @param taskDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody TaskDto taskDto) {
        taskService.updateTask(taskDto);
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc());
    }


    /**
     * 删除任务
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        taskService.deleteTaskByIds(ids);
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 更新任务周期表达式
     *
     * @param taskCronUpdateDto 任务周期表达式更新数据
     * @return 更新结果
     */
    @PostMapping("/updateCron")
    @MethodPermission("@dp.hasBtnPermission('updateCronOfScheduleTask')")
    public R<Void> updateCron(@RequestBody TaskCronUpdateDto taskCronUpdateDto) {
        logger.info("更新任务周期表达式：{}", taskCronUpdateDto);
        try {
            UserDto userDto = userinfoComponent.getUser();
            taskService.updateTaskCron(taskCronUpdateDto, userDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("更新任务周期表达式失败", e);
            if (e.getMessage().contains("同一方案下已存在相同cron表达式的任务")) {
                return R.fail(ResponseCodeEnum.DATA_ALREADY_EXISTS.getCode(), e.getMessage());
            } else if (e.getMessage().contains("参数不完整")) {
                return R.fail(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode(), e.getMessage());
            } else if (e.getMessage().contains("任务不存在")) {
                return R.fail(ResponseCodeEnum.UPDATE_DATA_NOT_FOUND.getCode(), e.getMessage());
            }
            return R.fail(ResponseCodeEnum.UPDATE_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("更新任务周期表达式系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 查询任务方案列表
     *
     * @param tableQueryDto 查询条件
     * @return 任务方案列表
     */
    @PostMapping("/taskPlanList")
    public R<PageInfo<TaskPlanListDto>> taskPlanList(@RequestBody TableQueryDto<TaskQueryDto> tableQueryDto) {
        logger.info("查询任务方案列表，查询条件：{}", tableQueryDto);

        PageInfo<TaskPlanListDto> list = taskService.selectTaskPlanList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * 操作任务（启动或停止）
     *
     * @param taskStartOrStopDto 任务启停参数
     * @return 操作结果
     */
    @PostMapping("/operate")
    @MethodPermission("@dp.hasBtnPermission('startScheduleTask') or @dp.hasBtnPermission('stopScheduleTask')")
    public R<Void> operateTasks(@RequestBody @Validated TaskStartOrStopDto taskStartOrStopDto) {
        logger.info("开始操作任务，参数：{}", taskStartOrStopDto);
        try {
            // 获取用户信息
            UserDto userDto = userinfoComponent.getUser();
            if (userDto == null || userDto.getId() == null) {
                logger.error("获取用户信息失败");
                return R.fail(ResponseCodeEnum.UNAUTHORIZED_ACCESS.getCode(), "用户未登录或登录已过期");
            }

            // 参数校验
            if (!TaskOperateEnums.isValidCode(taskStartOrStopDto.getOperateType())) {
                logger.error("操作类型不合法，operateType: {}", taskStartOrStopDto.getOperateType());
                return R.fail(ResponseCodeEnum.PARAM_FORMAT_ERROR.getCode(), "不支持的操作类型");
            }

            if (taskStartOrStopDto.getTaskIdList() == null || taskStartOrStopDto.getTaskIdList().isEmpty()) {
                logger.error("任务ID列表为空");
                return R.fail(ResponseCodeEnum.REQUIRED_FIELD_MISSING.getCode(), "任务ID列表不能为空");
            }

            // 执行任务操作
            taskService.operateTasks(taskStartOrStopDto, userDto);
            
            String operateTypeDesc = TaskOperateEnums.START.getCode().equals(taskStartOrStopDto.getOperateType()) ? "启动" : "停止";
            logger.info("任务{}操作成功，任务ID列表：{}", operateTypeDesc, taskStartOrStopDto.getTaskIdList());
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), "操作成功");

        } catch (ContrastBusinessException e) {
            logger.error("业务异常：任务操作失败", e);
            return R.fail(ResponseCodeEnum.UPDATE_FAIL.getCode(), e.getMessage());
        } catch (RuntimeException e) {
            if (e.getMessage() != null && e.getMessage().contains("已经处于")) {
                // 处理任务已经处于目标状态的情况
                logger.warn("任务状态提示：{}", e.getMessage());
                return R.fail(ResponseCodeEnum.UPDATE_FAIL.getCode(), e.getMessage());
            }
            logger.error("系统异常：任务操作失败", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), "系统异常，请稍后重试");
        } catch (Exception e) {
            logger.error("未知异常：任务操作失败", e);
            return R.fail(ResponseCodeEnum.UNKNOWN_ERROR.getCode(), "未知异常，请联系管理员");
        }
    }

    /**
     * 删除定时任务并删除任务
     *
     * @param taskIds 任务ID列表
     * @return 操作结果，包含成功和失败的任务信息
     */
    @PostMapping("/removeScheduleJob")
    @MethodPermission("@dp.hasBtnPermission('removeScheduleTask')")
    public R<TaskOperateResultDto> removeScheduleJob(@RequestBody List<Long> taskIds) {
        logger.info("删除定时任务并删除任务，任务ID列表：{}", taskIds);

        if (taskIds == null || taskIds.isEmpty()) {
            logger.warn("任务ID列表不能为空");
            return R.fail("13001", "任务ID列表不能为空");
        }

        try {
            UserDto userDto = userinfoComponent.getUser();
            TaskOperateResultDto result = taskService.removeScheduleJob(taskIds, userDto);

            if (result.isAllSuccess()) {
                logger.info("删除定时任务并删除任务成功，成功任务数：{}", result.getSuccessTaskIds().size());
                return R.ok(result);
            } else {
                logger.warn("删除定时任务并删除任务部分失败，成功任务数：{}，失败任务数：{}，失败原因：{}",
                        result.getSuccessTaskIds().size(), result.getFailedTaskIds().size(), result.getFailReason());
                return R.ok(result);
            }
        } catch (Exception e) {
            logger.error("删除定时任务并删除任务异常", e);
            return R.fail();
        }
    }
}
