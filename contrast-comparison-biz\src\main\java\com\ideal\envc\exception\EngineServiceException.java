package com.ideal.envc.exception;

/**
 * <AUTHOR>
 */
public class EngineServiceException extends Exception {

    public EngineServiceException(String message) {
        super(message);
    }

    public EngineServiceException(String code, String message) {
        super(code+message);
    }

    public EngineServiceException(String code, String message, Throwable cause) {
        super(code+message, cause);
    }

    public EngineServiceException(String message, Exception e) {
        super(message, e);
    }
    public EngineServiceException(Exception e) {
        super(e);
    }

    public EngineServiceException(Throwable e) {
        super(e);
    }

    public EngineServiceException(NoSuchMethodException e) {
        super(e);
    }
}
