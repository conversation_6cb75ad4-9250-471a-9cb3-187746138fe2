package com.ideal.envc.service.impl;

import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.envc.common.ContrastToolUtils;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.ResultMonitorMapper;
import com.ideal.envc.mapper.RunFlowResultMapper;
import com.ideal.envc.mapper.RunFlowMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.bean.ResultMonitorBean;
import com.ideal.envc.model.ContentDetailDto;
import com.ideal.envc.model.dto.ContentCustomDto;
import com.ideal.envc.model.dto.ResultMonitorDto;
import com.ideal.envc.model.dto.ResultMonitorQueryDto;
import com.ideal.envc.model.entity.RunFlowResultEntity;
import com.ideal.envc.model.entity.RunFlowEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.model.enums.RuleModelEnum;
import com.ideal.envc.service.IResultMonitorService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 比对结果监控Service实现
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Service
public class ResultMonitorServiceImpl implements IResultMonitorService {
    private final Logger logger = LoggerFactory.getLogger(ResultMonitorServiceImpl.class);

    private final ResultMonitorMapper resultMonitorMapper;
    private final RunFlowResultMapper runFlowResultMapper;
    private final RunFlowMapper runFlowMapper;
    private final RunRuleMapper runRuleMapper;

    public ResultMonitorServiceImpl(ResultMonitorMapper resultMonitorMapper, RunFlowResultMapper runFlowResultMapper, RunFlowMapper runFlowMapper, RunRuleMapper runRuleMapper) {
        this.resultMonitorMapper = resultMonitorMapper;
        this.runFlowResultMapper = runFlowResultMapper;
        this.runFlowMapper = runFlowMapper;
        this.runRuleMapper = runRuleMapper;
    }

    /**
     * 查询比对结果列表
     *
     * @param resultMonitorQueryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 比对结果列表
     */
    @Override
    public PageInfo<ResultMonitorDto> selectResultMonitorList(ResultMonitorQueryDto resultMonitorQueryDto, Integer pageNum, Integer pageSize) {
        logger.info("查询比对结果列表，查询条件：{}", resultMonitorQueryDto);

        // 参数校验
        if (resultMonitorQueryDto == null) {
            logger.warn("查询比对结果列表参数不完整，查询条件不能为空");
            return new PageInfo<>(new ArrayList<>());
        }
        if(resultMonitorQueryDto.getModel()==null){
            resultMonitorQueryDto.setModel(RuleModelEnum.COMPARE.getCode());
        }
        // 分页查询
        PageMethod.startPage(pageNum, pageSize);
        List<ResultMonitorBean> resultMonitorBeanList = resultMonitorMapper.selectResultMonitorList(
                resultMonitorQueryDto.getBusinessSystemName(),
                resultMonitorQueryDto.getModel(),
                resultMonitorQueryDto.getResult(),
                resultMonitorQueryDto.getFrom()
        );

        // 转换为DTO对象
        List<ResultMonitorDto> resultMonitorDtoList = BeanUtils.copy(resultMonitorBeanList, ResultMonitorDto.class);

        // 处理耗时格式
        for (ResultMonitorDto dto : resultMonitorDtoList) {
            // 从Bean列表中查找对应的Bean对象
            ResultMonitorBean correspondingBean = resultMonitorBeanList.stream()
                    .filter(bean -> bean.getId().equals(dto.getId()))
                    .findFirst()
                    .orElse(null);
            
            if (correspondingBean != null) {
                Long elapsedTime = correspondingBean.getElapsedTime();
                
                // 如果elapsedTime为null或0，说明任务正在运行中，需要计算当前耗时
                if (elapsedTime == null || elapsedTime == 0L) {
                    if (correspondingBean.getCreateTime() != null) {
                        elapsedTime = System.currentTimeMillis() - correspondingBean.getCreateTime().getTime();
                    } else {
                        elapsedTime = 0L;
                    }
                }
                
                dto.setElapsedTimeStr(ContrastToolUtils.formatElapsedTime(elapsedTime));
                
                // 设置触发方式的翻译
                if (correspondingBean.getFrom() != null) {
                    dto.setTriggerFrom(com.ideal.envc.model.enums.StartFromEnums.getNameByCode(correspondingBean.getFrom()));
                }
            }
        }

        PageInfo<ResultMonitorBean> pageInfo = new PageInfo<>(resultMonitorBeanList);
        return new PageInfo<>(resultMonitorDtoList);
    }

    /**
     * 根据流程ID查询比对详情
     *
     * @param flowId 流程ID
     * @return 比对详情
     */
    @Override
    public ContentDetailDto selectContentDetailByFlowId(Long flowId) throws ContrastBusinessException {
        logger.info("根据流程ID查询比对详情，flowId：{}", flowId);

        // 参数校验
        if (flowId == null) {
            logger.error("查询比对详情失败，flowId不能为空");
            throw new ContrastBusinessException("flowId不能为空");
        }

        try {
            // 根据flowId查询流程结果表
            RunFlowResultEntity runFlowResult = runFlowResultMapper.selectRunFlowResultByFlowId(flowId);

            if (runFlowResult == null) {
                logger.warn("根据flowId未查询到流程结果数据，flowId：{}", flowId);
                throw new ContrastBusinessException("未查询到对应的流程结果数据");
            }
            
            // 获取内容，优先使用icontent，如果为空则使用istderr
            String content = runFlowResult.getContent();
            if (StringUtils.isBlank(content)) {
                content = runFlowResult.getStderr();
            }

            ContentDetailDto contentDetailDto = new ContentDetailDto();

            // 判断内容是否包含分隔符@$@
            if (StringUtils.isNotBlank(content) && content.contains("@$@")) {
                String[] parts = content.split("@\\$@", 2);
                if (parts.length >= 2) {
                    contentDetailDto.setSourceContent(parts[0]);
                    contentDetailDto.setTargetContent(parts[1]);
                } else {
                    // 如果分隔后只有一部分，则作为sourceContent
                    contentDetailDto.setSourceContent(parts[0]);
                    contentDetailDto.setTargetContent("");
                }
            } else {
                // 不包含分隔符，整个内容作为sourceContent
                contentDetailDto.setSourceContent(content != null ? content : "");
                contentDetailDto.setTargetContent("");
            }

            logger.info("查询比对详情成功，flowId：{}", flowId);
            return contentDetailDto;

        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("查询比对详情失败，flowId：{}", flowId, e);
            throw new ContrastBusinessException("查询比对详情失败：" + e.getMessage());
        }
    }

    @Override
    public String selectContentForCompareFileByFlowId(Long flowId) throws ContrastBusinessException {
        logger.info("selectContentForCompareFileByFlowId 根据流程ID查询比对详情，flowId：{}", flowId);
        String content = "";
        // 参数校验
        if (flowId == null) {
            logger.error("selectContentForCompareFileByFlowId 查询比对详情失败，flowId不能为空");
            throw new ContrastBusinessException("flowId不能为空");
        }

        try {
            // 根据flowId查询流程结果表
            RunFlowResultEntity runFlowResult = runFlowResultMapper.selectRunFlowResultByFlowId(flowId);
           
            if (runFlowResult == null) {
                logger.warn(" selectContentForCompareFileByFlowId 根据flowId未查询到流程结果数据，flowId：{}", flowId);
                throw new ContrastBusinessException("未查询到对应的流程结果数据");
            }
            String sourcePath = "";
            String targetPath = "";
            //增加根据流程ID查询到对应运行规则的isourcePath和ipath路径
            RunFlowEntity runFlow = runFlowMapper.selectRunFlowByFlowId(flowId);
            if (runFlow != null) {
                RunRuleEntity runRule = runRuleMapper.selectRunRuleById(runFlow.getRunBizId());
                if (runRule != null) {
                    sourcePath = runRule.getSourcePath();
                    targetPath = runRule.getPath();
                }
            }

            // 获取内容，优先使用icontent，如果为空则使用istderr
            content = runFlowResult.getContent();
            if(StringUtils.isNotEmpty( content)){
                ContentCustomDto contentCustomDto = null;
                try{
                    contentCustomDto = JSON.parseObject(content, ContentCustomDto.class);
                    content = contentCustomDto.getContent();
                }catch (Exception e){
                    logger.error("selectContentForCompareFileByFlowId 解析contentCustomDto异常", e);
                }

            }
            if (StringUtils.isBlank(content)) {
                content = runFlowResult.getStderr();
            }
            if(content==null){
                content="";
            }
            // 查询源路径字符集
            content = content.replace("<span>Source</span>",
                    "<span><font class='compare_font'>Source</font>&nbsp;&nbsp;&nbsp;</span><span>" + sourcePath + "</span>");
            content = content.replace("<span>Target</span>",
                    "<span><font class='compare_font'>Target</font>&nbsp;&nbsp;&nbsp;</span><span>" + targetPath + "</span>");
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("selectContentForCompareFileByFlowId 处理异常", e);
            throw new ContrastBusinessException("查询比对详情失败：" + e.getMessage());
        }
        return content;
    }
}
