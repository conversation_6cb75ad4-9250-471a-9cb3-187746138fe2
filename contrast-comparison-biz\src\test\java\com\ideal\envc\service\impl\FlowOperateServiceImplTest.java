package com.ideal.envc.service.impl;

import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.interaction.engine.EngineInteract;
import com.ideal.envc.mapper.*;
import com.ideal.envc.model.bean.HierarchicalRunInstanceBean;
import com.ideal.envc.model.bean.RetryRuleBean;
import com.ideal.envc.model.dto.FlowOperateResultDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.start.StartTaskFlowDto;
import com.ideal.envc.model.entity.*;
import com.ideal.envc.model.enums.StateEnum;
import com.ideal.envc.model.enums.TaskFlowOperationTypeEnum;
import com.ideal.envc.service.IStartContrastCommonBaseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class FlowOperateServiceImplTest {

    @InjectMocks
    private FlowOperateServiceImpl flowOperateService;

    @Mock
    private EngineInteract engineInteract;

    @Mock
    private RunFlowMapper runFlowMapper;

    @Mock
    private RunRuleMapper runRuleMapper;

    @Mock
    private RunInstanceMapper runInstanceMapper;

    @Mock
    private RunInstanceInfoMapper runInstanceInfoMapper;

    @Mock
    private RunRuleContentMapper runRuleContentMapper;

    @Mock
    private IStartContrastCommonBaseService startContrastCommonBaseService;

    private UserDto userDto;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");
    }

    @Test
    @DisplayName("测试正常终止流程的场景")
    void operateTerminatedFlow_Success() throws Exception {
        // 准备测试数据
        Long[] flowIds = new Long[]{1L, 2L};
        RunFlowEntity runFlowEntity = new RunFlowEntity();
        runFlowEntity.setFlowid(1L);
        runFlowEntity.setRunBizId(1L);
        
        RunRuleEntity runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);

        // 配置mock行为
        when(engineInteract.engineKillPauseResumeFlow(flowIds, TaskFlowOperationTypeEnum.TASK_FLOW_DEFAULT.getCode()))
                .thenReturn("");
        when(runFlowMapper.selectRunFlowByFlowId(anyLong())).thenReturn(runFlowEntity);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);

        // 执行测试
        FlowOperateResultDto result = flowOperateService.operateTerminatedFlow(flowIds, userDto);

        // 验证结果
        assertNotNull(result);
        assertArrayEquals(flowIds, result.getFlowIds());
        assertEquals("", result.getFailFlowIds());
        
        // 验证方法调用
        verify(runFlowMapper, times(2)).updateRunFlow(any());
        verify(runRuleMapper, times(2)).updateRunRule(any());
    }

    @Test
    @DisplayName("测试传入空flowIds的异常场景")
    void operateTerminatedFlow_WithEmptyFlowIds() {
        // 准备测试数据
        Long[] flowIds = new Long[]{};

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateTerminatedFlow(flowIds, userDto);
        });

        assertEquals("flowIds is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试正常重试流程的场景")
    void operateRetryFlow_Success() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);
        retryRuleBean.setRuleContent("test content");

        RunRuleEntity runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);
        RunInstanceInfoEntity runInstanceInfoEntity = new RunInstanceInfoEntity();
        runInstanceInfoEntity.setId(1L);
        RunInstanceEntity runInstanceEntity = new RunInstanceEntity();
        runInstanceEntity.setId(1L);
        RunFlowEntity runFlowEntity = new RunFlowEntity();
        runFlowEntity.setId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);
        when(runInstanceInfoMapper.selectRunInstanceInfoById(anyLong())).thenReturn(runInstanceInfoEntity);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(runInstanceEntity);
        when(runFlowMapper.selectRunFlowByFlowId(flowId)).thenReturn(runFlowEntity);
        when(startContrastCommonBaseService.buildTaskFlowDtoList(any(), any())).thenReturn(new ArrayList<>());
        when(startContrastCommonBaseService.startContrastSendAndUpdateState(any(), anyString())).thenReturn(true);

        // 执行测试
        FlowOperateResultDto result = flowOperateService.operateRetryFlow(flowId, userDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(flowId, result.getFlowIds()[0]);
        assertEquals("", result.getFailFlowIds());

        // 验证方法调用
        verify(runInstanceMapper).insertRunInstance(any());
        verify(runInstanceInfoMapper).insertRunInstanceInfo(any());
        verify(runRuleMapper).insertRunRule(any());
        verify(runRuleContentMapper).insertRunRuleContent(any());
        verify(runFlowMapper).insertRunFlow(any());
    }

    @Test
    @DisplayName("测试传入空flowId的异常场景")
    void operateRetryFlow_WithNullFlowId() {
        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(null, userDto);
        });

        assertEquals("flowId is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RetryRuleBean为空的异常场景")
    void operateRetryFlow_WithNullRetryRuleBean() throws Exception {
        // 准备测试数据
        Long flowId = 1L;

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(null);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("retryRuleBean is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RetryRuleBean的runRuleId为空的异常场景")
    void operateRetryFlow_WithNullRunRuleId() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(null);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("retryRuleBean runRuleId is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RetryRuleBean的runInstanceId为空的异常场景")
    void operateRetryFlow_WithNullRunInstanceId() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(null);
        retryRuleBean.setRunInstanceInfoId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("retryRuleBean runInstanceId is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RetryRuleBean的runInstanceInfoId为空的异常场景")
    void operateRetryFlow_WithNullRunInstanceInfoId() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(null);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("retryRuleBean runInstanceInfoId is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RunRuleEntity为空的异常场景")
    void operateRetryFlow_WithNullRunRuleEntity() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);

        RunInstanceEntity runInstanceEntity = new RunInstanceEntity();
        runInstanceEntity.setId(1L);
        RunInstanceInfoEntity runInstanceInfoEntity = new RunInstanceInfoEntity();
        runInstanceInfoEntity.setId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(runInstanceEntity);
        when(runInstanceInfoMapper.selectRunInstanceInfoById(anyLong())).thenReturn(runInstanceInfoEntity);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(null);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("runRuleEntity is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RunInstanceEntity为空的异常场景")
    void operateRetryFlow_WithNullRunInstanceEntity() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);

        RunRuleEntity runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(null);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("runInstanceEntity is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RunInstanceInfoEntity为空的异常场景")
    void operateRetryFlow_WithNullRunInstanceInfoEntity() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);

        RunRuleEntity runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);
        RunInstanceEntity runInstanceEntity = new RunInstanceEntity();
        runInstanceEntity.setId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(runInstanceEntity);
        when(runInstanceInfoMapper.selectRunInstanceInfoById(anyLong())).thenReturn(null);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("runInstanceInfoEntity is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试MQ发送失败的场景")
    void operateRetryFlow_WithMQSendFailure() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);
        retryRuleBean.setRuleContent("test content");

        RunRuleEntity runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);
        RunInstanceInfoEntity runInstanceInfoEntity = new RunInstanceInfoEntity();
        runInstanceInfoEntity.setId(1L);
        RunInstanceEntity runInstanceEntity = new RunInstanceEntity();
        runInstanceEntity.setId(1L);
        RunFlowEntity runFlowEntity = new RunFlowEntity();
        runFlowEntity.setId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);
        when(runInstanceInfoMapper.selectRunInstanceInfoById(anyLong())).thenReturn(runInstanceInfoEntity);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(runInstanceEntity);
        when(runFlowMapper.selectRunFlowByFlowId(flowId)).thenReturn(runFlowEntity);
        when(startContrastCommonBaseService.buildTaskFlowDtoList(any(), any())).thenReturn(new ArrayList<>());
        when(startContrastCommonBaseService.startContrastSendAndUpdateState(any(), anyString())).thenReturn(false);

        // 执行测试
        FlowOperateResultDto result = flowOperateService.operateRetryFlow(flowId, userDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(flowId, result.getFlowIds()[0]);
        assertEquals(flowId.toString(), result.getFailFlowIds());
    }

    @Test
    @DisplayName("测试EngineServiceException异常场景")
    void operateRetryFlow_WithEngineServiceException() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);
        retryRuleBean.setRuleContent("test content");

        RunRuleEntity runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);
        RunInstanceInfoEntity runInstanceInfoEntity = new RunInstanceInfoEntity();
        runInstanceInfoEntity.setId(1L);
        RunInstanceEntity runInstanceEntity = new RunInstanceEntity();
        runInstanceEntity.setId(1L);
        RunFlowEntity runFlowEntity = new RunFlowEntity();
        runFlowEntity.setId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);
        when(runInstanceInfoMapper.selectRunInstanceInfoById(anyLong())).thenReturn(runInstanceInfoEntity);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(runInstanceEntity);
        when(runFlowMapper.selectRunFlowByFlowId(flowId)).thenReturn(runFlowEntity);
        when(startContrastCommonBaseService.buildTaskFlowDtoList(any(), any())).thenReturn(new ArrayList<>());
        when(startContrastCommonBaseService.startContrastSendAndUpdateState(any(), anyString()))
                .thenThrow(new EngineServiceException("Engine service error"));

        // 执行测试并验证异常
        EngineServiceException exception = assertThrows(EngineServiceException.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("Engine service error", exception.getMessage());
    }
} 