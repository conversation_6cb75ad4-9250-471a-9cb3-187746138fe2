package com.ideal.envc.model.bean;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 比对结果列表Bean对象
 *
 * <AUTHOR>
 */
public class ResultMonitorBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 批次号（ieai_envc_run_instance表的IID）
     */
    private Long id;

    /**
     * 业务系统ID（ieai_envc_project表的ibusiness_system_id）
     */
    private Long businessSystemId;

    /**
     * 业务系统名称（ieai_envc_project表的ibusiness_system_name）
     */
    private String businessSystemName;

    /**
     * 对比类型（ieai_envc_run_rule的imodel）
     */
    private Integer model;

    /**
     * 比对时间（ieai_envc_run_rule的icreate_time）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 源服务器（ieai_envc_run_instance_info表中isource_computer_ip）
     */
    private String sourceComputerIp;

    /**
     * 目标服务器（ieai_envc_run_instance_info表中itarget_computer_ip）
     */
    private String targetComputerIp;

    /**
     * 路径（ieai_envc_run_rule表中ipath）
     */
    private String path;

    /**
     * 原路径（ieai_envc_run_rule表中isource_path）
     */
    private String sourcePath;

    /**
     * 耗时（ieai_envc_run_rule表中ielapsed_time）
     */
    private Long elapsedTime;

    /**
     * 对比结果（ieai_envc_run_rule表中iresult）
     */
    private Integer result;

    /**
     * 状态（ieai_envc_run_rule表中istate）
     */
    private Integer state;


    /**
     * 运行规则ID（ieai_envc_run_rule表的id）
     */
    private Long runRuleId;

    /**
     * 流程ID（ieai_envc_run_flow表的flowid）
     */
    private Long flowId;

    /**
     * 触发方式
     */
    private Integer from ;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    public Integer getModel() {
        return model;
    }

    public void setModel(Integer model) {
        this.model = model;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSourceComputerIp() {
        return sourceComputerIp;
    }

    public void setSourceComputerIp(String sourceComputerIp) {
        this.sourceComputerIp = sourceComputerIp;
    }

    public String getTargetComputerIp() {
        return targetComputerIp;
    }

    public void setTargetComputerIp(String targetComputerIp) {
        this.targetComputerIp = targetComputerIp;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public Long getElapsedTime() {
        return elapsedTime;
    }

    public void setElapsedTime(Long elapsedTime) {
        this.elapsedTime = elapsedTime;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Long getRunRuleId() {
        return runRuleId;
    }

    public void setRunRuleId(Long runRuleId) {
        this.runRuleId = runRuleId;
    }

    public Long getFlowId() {
        return flowId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public Integer getFrom() {
        return from;
    }

    public void setFrom(Integer from) {
        this.from = from;
    }

    @Override
    public String toString() {
        return "ResultMonitorBean{" +
                "id=" + id +
                ", businessSystemId=" + businessSystemId +
                ", businessSystemName='" + businessSystemName + '\'' +
                ", model=" + model +
                ", createTime=" + createTime +
                ", sourceComputerIp='" + sourceComputerIp + '\'' +
                ", targetComputerIp='" + targetComputerIp + '\'' +
                ", path='" + path + '\'' +
                ", sourcePath='" + sourcePath + '\'' +
                ", elapsedTime=" + elapsedTime +
                ", result=" + result +
                ", state=" + state +
                ", runRuleId=" + runRuleId +
                ", flowId=" + flowId +
                ", from=" + from +
                '}';
    }
}
