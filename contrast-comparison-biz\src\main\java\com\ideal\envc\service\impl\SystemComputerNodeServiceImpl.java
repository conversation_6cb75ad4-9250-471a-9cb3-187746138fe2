package com.ideal.envc.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.envc.exception.ContrastBusinessException;

import com.ideal.envc.mapper.NodeRelationMapper;
import com.ideal.envc.mapper.NodeRuleContentMapper;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.model.bean.SystemComputerNodeListBean;
import com.ideal.envc.model.dto.SystemComputerNodeListDto;
import com.ideal.envc.model.dto.SystemComputerNodeBatchDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.SystemComputerEntity;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.SystemComputerNodeMapper;
import com.ideal.envc.model.entity.SystemComputerNodeEntity;
import com.ideal.envc.service.ISystemComputerNodeService;
import com.ideal.envc.model.dto.SystemComputerNodeDto;
import com.ideal.envc.model.dto.SystemComputerNodeQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统与设备节点关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Service
public class SystemComputerNodeServiceImpl implements ISystemComputerNodeService {
    private final Logger logger = LoggerFactory.getLogger(SystemComputerNodeServiceImpl.class);

    private final SystemComputerNodeMapper systemComputerNodeMapper;
    private final NodeRelationMapper nodeRelationMapper;
    private final NodeRuleContentMapper nodeRuleContentMapper;
    private final SystemComputerMapper systemComputerMapper;
    private final BatchHandler batchHandler;

    public SystemComputerNodeServiceImpl(SystemComputerNodeMapper systemComputerNodeMapper, NodeRelationMapper nodeRelationMapper, NodeRuleContentMapper nodeRuleContentMapper, SystemComputerMapper systemComputerMapper, BatchHandler batchHandler) {
        this.systemComputerNodeMapper = systemComputerNodeMapper;
        this.nodeRelationMapper = nodeRelationMapper;
        this.nodeRuleContentMapper = nodeRuleContentMapper;
        this.systemComputerMapper = systemComputerMapper;
        this.batchHandler = batchHandler;
    }

    /**
     * 查询系统与设备节点关系
     *
     * @param id 系统与设备节点关系主键
     * @return 系统与设备节点关系
     */
    @Override
    public SystemComputerNodeDto selectSystemComputerNodeById(Long id) {
        SystemComputerNodeEntity systemComputerNode = systemComputerNodeMapper.selectSystemComputerNodeById(id);
        return BeanUtils.copy(systemComputerNode, SystemComputerNodeDto.class);
    }

    /**
     * 查询系统与设备节点关系列表
     *
     * @param systemComputerNodeQueryDto 系统与设备节点关系
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 系统与设备节点关系
     */
    @Override
    public PageInfo<SystemComputerNodeDto> selectSystemComputerNodeList(SystemComputerNodeQueryDto systemComputerNodeQueryDto, Integer pageNum, Integer pageSize) {
        SystemComputerNodeEntity query = BeanUtils.copy(systemComputerNodeQueryDto, SystemComputerNodeEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<SystemComputerNodeEntity> systemComputerNodeList = systemComputerNodeMapper.selectSystemComputerNodeList(query);
        return PageDataUtil.toDtoPage(systemComputerNodeList, SystemComputerNodeDto.class);
    }


    /**
     * 新增系统与设备节点关系
     *
     * @param systemComputerNodeDto 系统与设备节点关系
     * @return 结果
     */
    @Override
    public int insertSystemComputerNode(SystemComputerNodeDto systemComputerNodeDto) {
        SystemComputerNodeEntity systemComputerNode = BeanUtils.copy(systemComputerNodeDto, SystemComputerNodeEntity.class);
        return systemComputerNodeMapper.insertSystemComputerNode(systemComputerNode);
    }

    /**
     * 修改系统与设备节点关系
     *
     * @param systemComputerNodeDto 系统与设备节点关系
     * @return 结果
     */
    @Override
    public int updateSystemComputerNode(SystemComputerNodeDto systemComputerNodeDto) {
        SystemComputerNodeEntity systemComputerNode = BeanUtils.copy(systemComputerNodeDto, SystemComputerNodeEntity.class);
        return systemComputerNodeMapper.updateSystemComputerNode(systemComputerNode);
    }

    /**
     * 批量删除系统与设备节点关系
     *
     * @param ids 需要删除的系统与设备节点关系主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteSystemComputerNodeByIds(Long[] ids) {
        nodeRuleContentMapper.deleteNodeRuleContentBySystemComputerNodeIds(ids);
        nodeRelationMapper.deleteNodeRelationBySystemComputerNodeIds(ids);
        return systemComputerNodeMapper.deleteSystemComputerNodeByIds(ids);
    }



    /**
     * 检查节点是否已存在
     *
     * @param systemComputerNodeDto 系统与设备节点关系
     * @return 是否存在
     */
    @Override
    public boolean checkNodeExists(SystemComputerNodeDto systemComputerNodeDto) {
        logger.info("检查节点是否已存在：{}", systemComputerNodeDto);
        if (systemComputerNodeDto == null ||
                systemComputerNodeDto.getBusinessSystemId() == null ||
                systemComputerNodeDto.getSourceCenterId() == null ||
                systemComputerNodeDto.getTargetCenterId() == null ||
                systemComputerNodeDto.getSourceComputerId() == null ||
                systemComputerNodeDto.getTargetComputerId() == null) {
            logger.warn("检查节点是否已存在参数不完整");
            return false;
        }

        SystemComputerNodeEntity query = new SystemComputerNodeEntity();
        query.setBusinessSystemId(systemComputerNodeDto.getBusinessSystemId());
        query.setSourceCenterId(systemComputerNodeDto.getSourceCenterId());
        query.setTargetCenterId(systemComputerNodeDto.getTargetCenterId());
        query.setSourceComputerId(systemComputerNodeDto.getSourceComputerId());
        query.setTargetComputerId(systemComputerNodeDto.getTargetComputerId());

        List<SystemComputerNodeEntity> list = systemComputerNodeMapper.selectSystemComputerNodeList(query);
        return list != null && !list.isEmpty();
    }

    /**
     * 查询系统已绑定源目标设备列表
     *
     * @param systemComputerNodeQueryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 系统已绑定源目标设备列表
     */
    @Override
    public PageInfo<SystemComputerNodeListDto> selectSystemComputerNodeBeanList(SystemComputerNodeQueryDto systemComputerNodeQueryDto, Integer pageNum, Integer pageSize) {
        logger.info("查询系统已绑定源目标设备列表，查询条件：{}", systemComputerNodeQueryDto);

        if (systemComputerNodeQueryDto == null || systemComputerNodeQueryDto.getBusinessSystemId() == null) {
            logger.warn("查询系统已绑定源目标设备列表参数不完整，业务系统ID不能为空");
            return new PageInfo<>(Collections.emptyList());
        }

        // 分页查询
        PageMethod.startPage(pageNum, pageSize);
        List<SystemComputerNodeListBean> beans = systemComputerNodeMapper.selectSystemComputerNodeListByCondition(
                systemComputerNodeQueryDto.getBusinessSystemId(),
                systemComputerNodeQueryDto.getSourceCenterId(),
                systemComputerNodeQueryDto.getTargetCenterId(),
                systemComputerNodeQueryDto.getSourceComputerIp(),
                systemComputerNodeQueryDto.getTargetComputerIp()
        );

        // 转换为DTO对象
        List<SystemComputerNodeListDto> systemComputerNodeListDtoList = BeanUtils.copy(beans, SystemComputerNodeListDto.class);

        // 构建分页结果
        PageInfo<SystemComputerNodeListBean> pageInfo = new PageInfo<>(beans);
        PageInfo<SystemComputerNodeListDto> result = new PageInfo<>(systemComputerNodeListDtoList);
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setPages(pageInfo.getPages());

        return result;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, ContrastBusinessException.class})
    public int batchBindSystemComputerNode(SystemComputerNodeBatchDto batchDto, UserDto userDto) throws ContrastBusinessException {
        // 验证参数
        validateBatchBindParams(batchDto);
        
        // 获取设备信息映射
        Map<Long, SystemComputerEntity> computerMap = getComputerInfoMap(batchDto);
        
        // 获取源设备信息
        SystemComputerEntity sourceComputer = getSourceComputer(computerMap, batchDto.getSourceComputerId());
        
        // 构建节点列表
        List<SystemComputerNodeEntity> nodeList = buildNodeList(batchDto, userDto, computerMap, sourceComputer);
        
        // 过滤已存在的节点
        List<SystemComputerNodeEntity> filteredNodeList = filterExistingNodes(nodeList, batchDto, sourceComputer);
        
        // 批量保存
        return batchSaveNodes(filteredNodeList);
    }

    /**
     * 验证批量绑定参数
     *
     * @param batchDto 批量绑定参数
     * @throws ContrastBusinessException 参数验证异常
     */
    private void validateBatchBindParams(SystemComputerNodeBatchDto batchDto) throws ContrastBusinessException {
        if (batchDto == null || batchDto.getTargetComputerIdList() == null || batchDto.getTargetComputerIdList().isEmpty() 
                || batchDto.getSourceComputerId() == null) {
            logger.warn("批量绑定参数为空");
            throw new ContrastBusinessException(ResponseCodeEnum.ADD_PARAM_ERROR.getCode() + "：批量绑定参数不能为空");
        }
    }

    /**
     * 获取设备信息映射
     *
     * @param batchDto 批量绑定参数
     * @return 设备信息映射
     * @throws ContrastBusinessException 设备信息获取异常
     */
    private Map<Long, SystemComputerEntity> getComputerInfoMap(SystemComputerNodeBatchDto batchDto) throws ContrastBusinessException {
        List<Long> allComputerIds = new ArrayList<>(batchDto.getTargetComputerIdList());
        allComputerIds.add(batchDto.getSourceComputerId());
        
        Map<Long, SystemComputerEntity> computerMap = systemComputerMapper.selectComputerIpMapByIdsAndSystemId(
                allComputerIds, 
                batchDto.getBusinessSystemId()
        );

        if (computerMap.isEmpty()) {
            logger.warn("未找到设备信息");
            throw new ContrastBusinessException(ResponseCodeEnum.DATA_NOT_FOUND.getCode() + "：未找到相关设备信息");
        }
        
        return computerMap;
    }

    /**
     * 获取源设备信息
     *
     * @param computerMap 设备信息映射
     * @param sourceComputerId 源设备ID
     * @return 源设备信息
     * @throws ContrastBusinessException 源设备信息获取异常
     */
    private SystemComputerEntity getSourceComputer(Map<Long, SystemComputerEntity> computerMap, Long sourceComputerId) 
            throws ContrastBusinessException {
        SystemComputerEntity sourceComputer = computerMap.get(sourceComputerId);
        if (sourceComputer == null) {
            logger.warn("未找到源设备信息");
            throw new ContrastBusinessException(ResponseCodeEnum.DATA_NOT_FOUND.getCode() + "：未找到源设备信息");
        }
        return sourceComputer;
    }

    /**
     * 构建节点列表
     *
     * @param batchDto 批量绑定参数
     * @param userDto 用户信息
     * @param computerMap 设备信息映射
     * @param sourceComputer 源设备信息
     * @return 节点列表
     * @throws ContrastBusinessException 节点构建异常
     */
    private List<SystemComputerNodeEntity> buildNodeList(SystemComputerNodeBatchDto batchDto, UserDto userDto,
            Map<Long, SystemComputerEntity> computerMap, SystemComputerEntity sourceComputer) throws ContrastBusinessException {
        List<SystemComputerNodeEntity> nodeList = new ArrayList<>();
        
        for (Long targetComputerId : batchDto.getTargetComputerIdList()) {
            SystemComputerEntity targetComputer = computerMap.get(targetComputerId);
            if (targetComputer == null) {
                logger.warn("未找到目标设备信息：{}", targetComputerId);
                continue;
            }

            // 检查源设备和目标设备是否相同
            if (sourceComputer.getComputerId().equals(targetComputerId)) {
                logger.warn("源设备和目标设备不能相同，跳过：{}", targetComputerId);
                continue;
            }

            SystemComputerNodeEntity node = createNodeEntity(batchDto, userDto, sourceComputer, targetComputer);
            nodeList.add(node);
        }

        if (nodeList.isEmpty()) {
            throw new ContrastBusinessException(ResponseCodeEnum.DATA_NOT_FOUND.getCode() + "：没有有效的目标设备可以绑定");
        }
        
        return nodeList;
    }

    /**
     * 创建节点实体
     *
     * @param batchDto 批量绑定参数
     * @param userDto 用户信息
     * @param sourceComputer 源设备信息
     * @param targetComputer 目标设备信息
     * @return 节点实体
     */
    private SystemComputerNodeEntity createNodeEntity(SystemComputerNodeBatchDto batchDto, UserDto userDto,
            SystemComputerEntity sourceComputer, SystemComputerEntity targetComputer) {
        SystemComputerNodeEntity node = new SystemComputerNodeEntity();
        node.setBusinessSystemId(batchDto.getBusinessSystemId());
        node.setSourceCenterId(batchDto.getSourceCenterId());
        node.setTargetCenterId(batchDto.getTargetCenterId());
        node.setSourceComputerId(sourceComputer.getComputerId());
        node.setSourceComputerIp(sourceComputer.getComputerIp());
        node.setTargetComputerId(targetComputer.getComputerId());
        node.setTargetComputerIp(targetComputer.getComputerIp());
        node.setCreatorId(userDto.getId());
        node.setCreatorName(userDto.getFullName());
        return node;
    }

    /**
     * 过滤已存在的节点
     *
     * @param nodeList 节点列表
     * @param batchDto 批量绑定参数
     * @param sourceComputer 源设备信息
     * @return 过滤后的节点列表
     * @throws ContrastBusinessException 过滤异常
     */
    private List<SystemComputerNodeEntity> filterExistingNodes(List<SystemComputerNodeEntity> nodeList,
            SystemComputerNodeBatchDto batchDto, SystemComputerEntity sourceComputer) throws ContrastBusinessException {
        SystemComputerNodeEntity queryEntity = new SystemComputerNodeEntity();
        queryEntity.setBusinessSystemId(batchDto.getBusinessSystemId());
        queryEntity.setSourceCenterId(batchDto.getSourceCenterId());
        queryEntity.setTargetCenterId(batchDto.getTargetCenterId());
        queryEntity.setSourceComputerId(sourceComputer.getComputerId());
        
        List<SystemComputerNodeEntity> existingNodes = systemComputerNodeMapper.selectSystemComputerNodeList(queryEntity);

        if (!existingNodes.isEmpty()) {
            // 过滤掉已存在的记录
            List<Long> existingTargetComputerIds = existingNodes.stream()
                    .map(SystemComputerNodeEntity::getTargetComputerId)
                    .collect(Collectors.toList());

            nodeList = nodeList.stream()
                    .filter(node -> !existingTargetComputerIds.contains(node.getTargetComputerId()))
                    .collect(Collectors.toList());

            if (nodeList.isEmpty()) {
                throw new ContrastBusinessException(ResponseCodeEnum.DATA_ALREADY_EXISTS.getCode() + "：所有目标设备都已存在绑定关系");
            }
        }
        
        return nodeList;
    }

    /**
     * 批量保存节点
     *
     * @param nodeList 节点列表
     * @return 保存的节点数量
     * @throws ContrastBusinessException 保存异常
     */
    private int batchSaveNodes(List<SystemComputerNodeEntity> nodeList) throws ContrastBusinessException {
        try {
            batchHandler.batchData(nodeList, systemComputerNodeMapper::insertSystemComputerNode, 500);
            return nodeList.size();
        } catch (Exception e) {
            logger.error("批量保存节点关系失败", e);
            throw new ContrastBusinessException(ResponseCodeEnum.ADD_FAIL.getCode() + "：批量保存节点关系失败");
        }
    }
}
